# Railway Deployment Guide

This guide explains how to deploy the Trodoo backend service to Railway.

## 🚨 Important Note

Railway **does not support Docker Compose**. The `docker-compose.yml` file is for local development only. For Railway deployment, we use the `Dockerfile` and `railway.toml` configuration.

## ✅ Problem Fixed

**Issue**: The application was failing to start on Railway because services were throwing errors when detecting placeholder environment variables in production mode.

**Solution**: Modified all services to gracefully fall back to mock mode instead of throwing errors, allowing the application to start successfully even with placeholder values. The health endpoint now clearly indicates when services are running in mock mode.

## 🚀 Quick Deployment

### 1. Connect Repository to Railway

1. Go to [Railway.app](https://railway.app)
2. Click "New Project" → "Deploy from GitHub repo"
3. Select your repository
4. Choose the `backend` folder as the root directory

### 2. Configure Environment Variables

In the Railway dashboard, set these **required** environment variables:

```bash
# PocketBase Configuration (Required)
POCKETBASE_URL=https://trodoorentals.pockethost.io
POCKETBASE_ADMIN_EMAIL=your_admin_email
POCKETBASE_ADMIN_PASSWORD=your_admin_password

# Email Service (Required)
RESEND_API_KEY=re_your_resend_api_key
FROM_EMAIL=<EMAIL>
FROM_NAME=Trodoo

# Paystack Configuration (Required)
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public
PAYSTACK_WEBHOOK_SECRET=your_webhook_secret

# Frontend URL
FRONTEND_URL=https://your-frontend-domain.com

# Service Configuration
LOG_LEVEL=INFO
ENABLE_CRON_JOBS=true
```

### 3. Optional Services

#### MeiliSearch (Search functionality)
If you want search functionality, add:
```bash
MEILISEARCH_HOST=https://your-meilisearch-instance.com
MEILISEARCH_API_KEY=your_meilisearch_api_key
```

**Note**: If not configured, the service will run in mock mode for search.

#### Redis (Caching)
Add Railway's Redis service or use external Redis:
```bash
REDIS_URL=redis://your-redis-instance:6379
```

## 🔧 Railway Configuration

The `railway.toml` file configures:
- **Builder**: Uses Dockerfile
- **Health Check**: `/health` endpoint
- **Port**: 8000 (automatically mapped by Railway)
- **Restart Policy**: On failure with max 3 retries

## 📋 Deployment Steps

1. **Push your code** to GitHub
2. **Connect to Railway** and select the backend folder
3. **Set environment variables** in Railway dashboard
4. **Deploy** - Railway will automatically build and deploy

## 🔍 Monitoring

### Health Check
Railway will monitor the `/health` endpoint. The service includes:
- Health check endpoint at `/health`
- Automatic restart on failure
- Logging for debugging

The health endpoint returns detailed information:
```json
{
  "status": "healthy",
  "timestamp": "2025-06-23T11:06:21.415Z",
  "version": "1.0.0",
  "environment": "production",
  "mode": "mock",
  "services": {
    "email": true,
    "notification": true,
    "meilisearch": true,
    "paystack": true,
    "payout": true
  },
  "message": "Services running in mock mode - configure environment variables for production"
}
```

**Mode Indicators**:
- `"mode": "mock"` - Services running with placeholder values
- `"mode": "production"` - Services running with real credentials

### Logs
View logs in Railway dashboard:
- Build logs during deployment
- Runtime logs for debugging
- Error tracking

## 🚨 Troubleshooting

### Common Issues

1. **"docker-compose not found"**
   - This error occurs if Railway tries to run docker-compose
   - Solution: Ensure Railway is using the Dockerfile, not docker-compose.yml

2. **Environment variables not set**
   - Service will run in mock mode for missing optional variables
   - Required variables will cause startup failure

3. **Port binding issues**
   - Railway automatically handles port mapping
   - Ensure your service listens on the PORT environment variable

### Debug Steps

1. Check Railway build logs
2. Verify environment variables are set
3. Test health endpoint: `https://your-app.railway.app/health`
4. Check runtime logs for errors

## 🔄 Updates

To update your deployment:
1. Push changes to GitHub
2. Railway will automatically rebuild and deploy
3. Monitor deployment in Railway dashboard

## 📞 Support

For Railway-specific issues:
- Check [Railway Documentation](https://docs.railway.app)
- Visit [Railway Discord](https://discord.gg/railway)

For application issues:
- Check the application logs in Railway dashboard
- Verify environment variables are correctly set
