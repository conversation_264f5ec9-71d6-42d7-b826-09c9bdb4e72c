// Email service wrapper for Resend integration
import { Resend, delay } from "../../deps.ts";
import { createLogger, PerformanceLogger } from "../utils/logger.ts";

const logger = createLogger("EmailService");

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailData {
  to: string | string[];
  from?: string;
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: string; // base64 encoded
    contentType: string;
  }>;
}

export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

class EmailService {
  private resend: Resend | null = null;
  private fromEmail: string;
  private fromName: string;
  private maxRetries: number;
  private retryDelay: number;
  private isInitialized = false;

  constructor() {
    this.fromEmail = Deno.env.get("FROM_EMAIL") || "<EMAIL>";
    this.fromName = Deno.env.get("FROM_NAME") || "Trodoo";
    this.maxRetries = parseInt(Deno.env.get("MAX_RETRY_ATTEMPTS") || "3");
    this.retryDelay = parseInt(Deno.env.get("RETRY_DELAY_MS") || "1000");
  }

  async initialize(): Promise<void> {
    const apiKey = Deno.env.get("RESEND_API_KEY");
    const nodeEnv = Deno.env.get("NODE_ENV") || "development";

    if (!apiKey || apiKey.startsWith("re_your_")) {
      if (nodeEnv === "production") {
        throw new Error("RESEND_API_KEY environment variable is required in production");
      }

      // Development mode - use mock service
      logger.warn("Email service running in mock mode (no valid API key provided)");
      this.resend = null;
      this.isInitialized = true;
      return;
    }

    this.resend = new Resend(apiKey);
    this.isInitialized = true;

    logger.info("Email service initialized", {
      fromEmail: this.fromEmail,
      fromName: this.fromName,
    });
  }

  isHealthy(): boolean {
    return this.isInitialized;
  }

  async sendEmail(emailData: EmailData): Promise<EmailResult> {
    if (!this.isInitialized) {
      return { success: false, error: "Email service not initialized" };
    }

    const perfLogger = new PerformanceLogger("sendEmail");

    try {
      // Mock mode - just log the email
      if (!this.resend) {
        logger.info("Mock email sent", {
          to: emailData.to,
          subject: emailData.subject,
          from: emailData.from || `${this.fromName} <${this.fromEmail}>`,
        });

        perfLogger.end({ to: emailData.to, subject: emailData.subject });
        return {
          success: true,
          messageId: `mock-${Date.now()}`,
        };
      }

      const result = await this.sendWithRetry(emailData);
      perfLogger.end({ to: emailData.to, subject: emailData.subject });
      return result;
    } catch (error) {
      perfLogger.endWithError(error);
      return { success: false, error: error.message };
    }
  }

  private async sendWithRetry(emailData: EmailData, attempt = 1): Promise<EmailResult> {
    try {
      const response = await this.resend!.emails.send({
        from: emailData.from || `${this.fromName} <${this.fromEmail}>`,
        to: emailData.to,
        subject: emailData.subject,
        html: emailData.html,
        text: emailData.text,
        attachments: emailData.attachments,
      });

      logger.info("Email sent successfully", {
        to: emailData.to,
        subject: emailData.subject,
        messageId: response.data?.id,
      });

      return {
        success: true,
        messageId: response.data?.id,
      };
    } catch (error) {
      logger.warn(`Email send attempt ${attempt} failed`, error, {
        to: emailData.to,
        subject: emailData.subject,
      });

      if (attempt < this.maxRetries) {
        await delay(this.retryDelay * attempt); // Exponential backoff
        return this.sendWithRetry(emailData, attempt + 1);
      }

      throw error;
    }
  }

  // Template methods for different email types
  async sendWelcomeEmail(userEmail: string, userName: string): Promise<EmailResult> {
    const template = this.getWelcomeTemplate(userName);
    
    return this.sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendBookingConfirmationEmail(
    userEmail: string,
    userName: string,
    bookingDetails: {
      venueName: string;
      startDate: string;
      endDate: string;
      totalPrice: number;
    }
  ): Promise<EmailResult> {
    const template = this.getBookingConfirmationTemplate(userName, bookingDetails);
    
    return this.sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendPaymentSuccessEmail(
    userEmail: string,
    userName: string,
    bookingDetails: {
      venueName: string;
      startDate: string;
      endDate: string;
      totalPrice: number;
      reference: string;
    }
  ): Promise<EmailResult> {
    const template = this.getPaymentSuccessTemplate(userName, bookingDetails);
    
    return this.sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendCheckinReminderEmail(
    userEmail: string,
    userName: string,
    bookingDetails: {
      venueName: string;
      venueAddress: string;
      startDate: string;
    }
  ): Promise<EmailResult> {
    const template = this.getCheckinReminderTemplate(userName, bookingDetails);
    
    return this.sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendCheckoutReminderEmail(
    userEmail: string,
    userName: string,
    bookingDetails: {
      venueName: string;
      endDate: string;
      checklistUrl?: string;
    }
  ): Promise<EmailResult> {
    const template = this.getCheckoutReminderTemplate(userName, bookingDetails);
    
    return this.sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendReviewPromptEmail(
    userEmail: string,
    userName: string,
    bookingDetails: {
      venueName: string;
      reviewUrl: string;
    }
  ): Promise<EmailResult> {
    const template = this.getReviewPromptTemplate(userName, bookingDetails);
    
    return this.sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendGuestInvitationEmail(
    guestEmail: string,
    inviterName: string,
    eventDetails: {
      venueName: string;
      venueAddress: string;
      startDate: string;
      endDate: string;
      message?: string;
    }
  ): Promise<EmailResult> {
    const template = this.getGuestInvitationTemplate(inviterName, eventDetails);

    return this.sendEmail({
      to: guestEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  async sendPasswordResetEmail(
    userEmail: string,
    userName: string,
    resetToken: string
  ): Promise<EmailResult> {
    const template = this.getPasswordResetTemplate(userName, resetToken);

    return this.sendEmail({
      to: userEmail,
      subject: template.subject,
      html: template.html,
      text: template.text,
    });
  }

  // Template generation methods
  private getWelcomeTemplate(userName: string): EmailTemplate {
    const frontendUrl = Deno.env.get("FRONTEND_URL") || "http://localhost:4321";
    
    return {
      subject: "Welcome to Trodoo - Your Space, Your Success",
      html: `
        <div style="font-family: 'Roboto', Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #059669; font-family: 'Poppins', sans-serif; font-size: 28px; margin: 0;">
              Welcome to Trodoo
            </h1>
            <p style="color: #6B7280; font-size: 16px; margin: 10px 0 0 0;">
              Your Space, Your Success
            </p>
          </div>
          
          <div style="background: #F9FAFB; padding: 30px; border-radius: 12px; margin-bottom: 30px;">
            <h2 style="color: #1F2937; font-size: 20px; margin: 0 0 15px 0;">
              Hello ${userName}!
            </h2>
            <p style="color: #6B7280; line-height: 1.6; margin: 0 0 20px 0;">
              Thank you for joining Trodoo, the premier platform for venue rentals. 
              We're excited to help you find the perfect space for your events or 
              list your venue to reach quality renters.
            </p>
            
            <div style="margin: 25px 0;">
              <h3 style="color: #059669; font-size: 16px; margin: 0 0 10px 0;">
                Get Started:
              </h3>
              <ul style="color: #6B7280; line-height: 1.6; margin: 0; padding-left: 20px;">
                <li>Browse our curated selection of venues</li>
                <li>List your own venue to start earning</li>
                <li>Connect with our community of professionals</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
              <a href="${frontendUrl}" 
                 style="background: #F59E0B; color: #1F2937; padding: 12px 24px; 
                        text-decoration: none; border-radius: 50px; font-weight: 700;
                        display: inline-block;">
                Explore Venues
              </a>
            </div>
          </div>
          
          <div style="text-align: center; color: #9CA3AF; font-size: 14px;">
            <p>Need help? Contact <NAME_EMAIL></p>
            <p style="margin: 10px 0 0 0;">
              © 2025 Trodoo. All rights reserved.
            </p>
          </div>
        </div>
      `,
      text: `Welcome to Trodoo, ${userName}!\n\nThank you for joining our platform. Visit ${frontendUrl} to get started.\n\nBest regards,\nThe Trodoo Team`,
    };
  }

  private getBookingConfirmationTemplate(
    userName: string,
    bookingDetails: {
      venueName: string;
      startDate: string;
      endDate: string;
      totalPrice: number;
    }
  ): EmailTemplate {
    const startDate = new Date(bookingDetails.startDate).toLocaleDateString();
    const endDate = new Date(bookingDetails.endDate).toLocaleDateString();
    
    return {
      subject: `Booking Confirmed - ${bookingDetails.venueName}`,
      html: `
        <div style="font-family: 'Roboto', Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #D1FAE5; padding: 20px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: #059669; font-size: 24px; margin: 0;">
              Booking Confirmed!
            </h1>
          </div>
          
          <p style="color: #1F2937; font-size: 16px; margin-bottom: 20px;">
            Hello ${userName},
          </p>
          
          <p style="color: #6B7280; line-height: 1.6; margin-bottom: 30px;">
            Great news! Your booking has been confirmed. Here are the details:
          </p>
          
          <div style="background: #F9FAFB; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
            <h2 style="color: #059669; font-size: 18px; margin: 0 0 15px 0;">
              ${bookingDetails.venueName}
            </h2>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Start:</strong> ${startDate}</p>
            <p style="color: #6B7280; margin: 5px 0;"><strong>End:</strong> ${endDate}</p>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Total:</strong> ₦${bookingDetails.totalPrice.toLocaleString()}</p>
          </div>
          
          <p style="color: #6B7280; line-height: 1.6; margin-bottom: 20px;">
            You'll receive a payment link shortly. Once payment is completed, 
            you'll have access to venue messaging and additional booking details.
          </p>
          
          <div style="text-align: center; color: #9CA3AF; font-size: 14px; margin-top: 40px;">
            <p>© 2025 Trodoo. All rights reserved.</p>
          </div>
        </div>
      `,
      text: `Booking Confirmed!\n\nHello ${userName},\n\nYour booking for ${bookingDetails.venueName} has been confirmed.\n\nDetails:\n- Start: ${startDate}\n- End: ${endDate}\n- Total: ₦${bookingDetails.totalPrice.toLocaleString()}\n\nBest regards,\nThe Trodoo Team`,
    };
  }

  // Additional template methods would continue here...
  // For brevity, I'll implement the core ones and add more as needed

  private getPaymentSuccessTemplate(
    userName: string,
    bookingDetails: {
      venueName: string;
      startDate: string;
      endDate: string;
      totalPrice: number;
      reference: string;
    }
  ): EmailTemplate {
    return {
      subject: "Payment Successful - Your booking is confirmed!",
      html: `
        <div style="font-family: 'Roboto', Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #D1FAE5; padding: 20px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: #059669; font-size: 24px; margin: 0;">
              Payment Successful!
            </h1>
            <p style="color: #047857; margin: 10px 0 0 0;">
              Your booking is now confirmed
            </p>
          </div>
          
          <p style="color: #1F2937; font-size: 16px; margin-bottom: 20px;">
            Hello ${userName},
          </p>
          
          <p style="color: #6B7280; line-height: 1.6; margin-bottom: 30px;">
            Your payment has been processed successfully! You can now access 
            venue messaging and all booking features.
          </p>
          
          <div style="background: #F9FAFB; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
            <h2 style="color: #059669; font-size: 18px; margin: 0 0 15px 0;">
              ${bookingDetails.venueName}
            </h2>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Amount Paid:</strong> ₦${bookingDetails.totalPrice.toLocaleString()}</p>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Reference:</strong> ${bookingDetails.reference}</p>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Date:</strong> ${new Date(bookingDetails.startDate).toLocaleDateString()}</p>
          </div>
          
          <div style="text-align: center; color: #9CA3AF; font-size: 14px; margin-top: 40px;">
            <p>© 2025 Trodoo. All rights reserved.</p>
          </div>
        </div>
      `,
      text: `Payment Successful!\n\nHello ${userName},\n\nYour payment for ${bookingDetails.venueName} has been processed.\n\nAmount: ₦${bookingDetails.totalPrice.toLocaleString()}\nReference: ${bookingDetails.reference}\n\nBest regards,\nThe Trodoo Team`,
    };
  }

  private getCheckinReminderTemplate(
    userName: string,
    bookingDetails: {
      venueName: string;
      venueAddress: string;
      startDate: string;
    }
  ): EmailTemplate {
    const startTime = new Date(bookingDetails.startDate).toLocaleString();
    const mapsUrl = `https://maps.google.com/?q=${encodeURIComponent(bookingDetails.venueAddress)}`;

    return {
      subject: `Check-in Reminder - ${bookingDetails.venueName}`,
      html: `
        <div style="font-family: 'Roboto', Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #FEF3C7; padding: 20px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: #D97706; font-size: 24px; margin: 0;">
              Check-in Soon!
            </h1>
          </div>

          <p style="color: #1F2937; font-size: 16px; margin-bottom: 20px;">
            Hello ${userName},
          </p>

          <p style="color: #6B7280; line-height: 1.6; margin-bottom: 30px;">
            Your booking at <strong>${bookingDetails.venueName}</strong> starts soon!
          </p>

          <div style="background: #F9FAFB; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
            <h2 style="color: #D97706; font-size: 18px; margin: 0 0 15px 0;">
              Venue Details
            </h2>
            <p style="color: #6B7280; margin: 5px 0;">
              <strong>Location:</strong>
              <a href="${mapsUrl}" style="color: #059669; text-decoration: none;">
                ${bookingDetails.venueAddress}
              </a>
            </p>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Check-in Time:</strong> ${startTime}</p>
          </div>

          <div style="text-align: center; color: #9CA3AF; font-size: 14px; margin-top: 40px;">
            <p>© 2025 Trodoo. All rights reserved.</p>
          </div>
        </div>
      `,
      text: `Check-in Reminder\n\nHello ${userName},\n\nYour booking at ${bookingDetails.venueName} starts soon!\n\nLocation: ${bookingDetails.venueAddress}\nTime: ${startTime}\n\nBest regards,\nThe Trodoo Team`,
    };
  }

  private getCheckoutReminderTemplate(
    userName: string,
    bookingDetails: {
      venueName: string;
      endDate: string;
      checklistUrl?: string;
    }
  ): EmailTemplate {
    const endTime = new Date(bookingDetails.endDate).toLocaleString();
    
    return {
      subject: `Check-out Reminder - ${bookingDetails.venueName}`,
      html: `
        <div style="font-family: 'Roboto', Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #FEF3C7; padding: 20px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: #D97706; font-size: 24px; margin: 0;">
              Check-out Reminder
            </h1>
          </div>
          
          <p style="color: #1F2937; font-size: 16px; margin-bottom: 20px;">
            Hello ${userName},
          </p>
          
          <p style="color: #6B7280; line-height: 1.6; margin-bottom: 30px;">
            Your booking at <strong>${bookingDetails.venueName}</strong> ends soon. 
            Please ensure you complete the check-out process.
          </p>
          
          <div style="background: #F9FAFB; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
            <p style="color: #6B7280; margin: 5px 0;"><strong>Check-out Time:</strong> ${endTime}</p>
            ${bookingDetails.checklistUrl ? `
              <div style="margin-top: 20px; text-align: center;">
                <a href="${bookingDetails.checklistUrl}" 
                   style="background: #F59E0B; color: #1F2937; padding: 12px 24px; 
                          text-decoration: none; border-radius: 50px; font-weight: 700;
                          display: inline-block;">
                  Complete Check-out Checklist
                </a>
              </div>
            ` : ''}
          </div>
          
          <div style="text-align: center; color: #9CA3AF; font-size: 14px; margin-top: 40px;">
            <p>© 2025 Trodoo. All rights reserved.</p>
          </div>
        </div>
      `,
      text: `Check-out Reminder\n\nHello ${userName},\n\nYour booking at ${bookingDetails.venueName} ends soon.\n\nCheck-out Time: ${endTime}\n\n${bookingDetails.checklistUrl ? `Complete checklist: ${bookingDetails.checklistUrl}\n\n` : ''}Best regards,\nThe Trodoo Team`,
    };
  }

  private getReviewPromptTemplate(
    userName: string,
    bookingDetails: {
      venueName: string;
      reviewUrl: string;
    }
  ): EmailTemplate {
    return {
      subject: `How was your experience at ${bookingDetails.venueName}?`,
      html: `
        <div style="font-family: 'Roboto', Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #D1FAE5; padding: 20px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: #059669; font-size: 24px; margin: 0;">
              Share Your Experience
            </h1>
          </div>
          
          <p style="color: #1F2937; font-size: 16px; margin-bottom: 20px;">
            Hello ${userName},
          </p>
          
          <p style="color: #6B7280; line-height: 1.6; margin-bottom: 30px;">
            We hope you had a great experience at <strong>${bookingDetails.venueName}</strong>! 
            Your feedback helps other users make informed decisions and helps venue owners improve their service.
          </p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${bookingDetails.reviewUrl}" 
               style="background: #F59E0B; color: #1F2937; padding: 12px 24px; 
                      text-decoration: none; border-radius: 50px; font-weight: 700;
                      display: inline-block;">
              Leave a Review
            </a>
          </div>
          
          <p style="color: #9CA3AF; font-size: 14px; text-align: center; margin-top: 30px;">
            This will only take a minute and helps our community grow stronger.
          </p>
          
          <div style="text-align: center; color: #9CA3AF; font-size: 14px; margin-top: 40px;">
            <p>© 2025 Trodoo. All rights reserved.</p>
          </div>
        </div>
      `,
      text: `Share Your Experience\n\nHello ${userName},\n\nWe hope you had a great experience at ${bookingDetails.venueName}!\n\nPlease leave a review: ${bookingDetails.reviewUrl}\n\nBest regards,\nThe Trodoo Team`,
    };
  }

  private getGuestInvitationTemplate(
    inviterName: string,
    eventDetails: {
      venueName: string;
      venueAddress: string;
      startDate: string;
      endDate: string;
      message?: string;
    }
  ): EmailTemplate {
    const startTime = new Date(eventDetails.startDate).toLocaleString();
    const endTime = new Date(eventDetails.endDate).toLocaleString();

    return {
      subject: `You're invited to an event at ${eventDetails.venueName}`,
      html: `
        <div style="font-family: 'Roboto', Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #D1FAE5; padding: 20px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: #059669; font-size: 24px; margin: 0;">
              You're Invited!
            </h1>
          </div>

          <p style="color: #1F2937; font-size: 16px; margin-bottom: 20px;">
            Hello!
          </p>

          <p style="color: #6B7280; line-height: 1.6; margin-bottom: 30px;">
            <strong>${inviterName}</strong> has invited you to an event at <strong>${eventDetails.venueName}</strong>.
          </p>

          ${eventDetails.message ? `
            <div style="background: #F9FAFB; padding: 20px; border-radius: 12px; margin-bottom: 30px; border-left: 4px solid #059669;">
              <p style="color: #6B7280; margin: 0; font-style: italic;">
                "${eventDetails.message}"
              </p>
            </div>
          ` : ''}

          <div style="background: #F9FAFB; padding: 25px; border-radius: 12px; margin-bottom: 30px;">
            <h2 style="color: #059669; font-size: 18px; margin: 0 0 15px 0;">
              Event Details
            </h2>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Venue:</strong> ${eventDetails.venueName}</p>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Location:</strong> ${eventDetails.venueAddress}</p>
            <p style="color: #6B7280; margin: 5px 0;"><strong>Start:</strong> ${startTime}</p>
            <p style="color: #6B7280; margin: 5px 0;"><strong>End:</strong> ${endTime}</p>
          </div>

          <p style="color: #9CA3AF; font-size: 14px; text-align: center; margin-top: 30px;">
            This invitation was sent through Trodoo, a premium venue booking platform.
          </p>

          <div style="text-align: center; color: #9CA3AF; font-size: 14px; margin-top: 40px;">
            <p>© 2025 Trodoo. All rights reserved.</p>
          </div>
        </div>
      `,
      text: `You're Invited!\n\n${inviterName} has invited you to an event at ${eventDetails.venueName}.\n\n${eventDetails.message ? `Message: "${eventDetails.message}"\n\n` : ''}Event Details:\n- Venue: ${eventDetails.venueName}\n- Location: ${eventDetails.venueAddress}\n- Start: ${startTime}\n- End: ${endTime}\n\nSent via Trodoo`,
    };
  }

  private getPasswordResetTemplate(
    userName: string,
    resetToken: string
  ): EmailTemplate {
    const frontendUrl = Deno.env.get("FRONTEND_URL") || "http://localhost:4321";
    const resetUrl = `${frontendUrl}/auth/reset-password?token=${resetToken}`;

    return {
      subject: "Reset Your Trodoo Password",
      html: `
        <div style="font-family: 'Roboto', Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #059669; font-family: 'Poppins', sans-serif; font-size: 28px; margin: 0;">
              Trodoo
            </h1>
            <p style="color: #6B7280; font-size: 16px; margin: 10px 0 0 0;">
              Password Reset Request
            </p>
          </div>

          <div style="background: #FEF3C7; padding: 20px; border-radius: 12px; text-align: center; margin-bottom: 30px;">
            <h2 style="color: #D97706; font-size: 24px; margin: 0;">
              Reset Your Password
            </h2>
          </div>

          <p style="color: #1F2937; font-size: 16px; margin-bottom: 20px;">
            Hello ${userName},
          </p>

          <p style="color: #6B7280; line-height: 1.6; margin-bottom: 30px;">
            We received a request to reset your password for your Trodoo account.
            Click the button below to create a new password.
          </p>

          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}"
               style="background: #F59E0B; color: #1F2937; padding: 15px 30px;
                      text-decoration: none; border-radius: 50px; font-weight: 700;
                      display: inline-block; font-size: 16px;">
              Reset My Password
            </a>
          </div>

          <div style="background: #F9FAFB; padding: 20px; border-radius: 12px; margin-bottom: 30px;">
            <p style="color: #6B7280; margin: 0; font-size: 14px;">
              <strong>Security Note:</strong> This link will expire in 1 hour for your security.
              If you didn't request this password reset, you can safely ignore this email.
            </p>
          </div>

          <p style="color: #6B7280; font-size: 14px; margin-bottom: 20px;">
            If the button doesn't work, copy and paste this link into your browser:
          </p>
          <p style="color: #059669; font-size: 14px; word-break: break-all; margin-bottom: 30px;">
            ${resetUrl}
          </p>

          <div style="text-align: center; color: #9CA3AF; font-size: 14px; margin-top: 40px;">
            <p>Need help? Contact <NAME_EMAIL></p>
            <p style="margin: 10px 0 0 0;">
              © 2025 Trodoo. All rights reserved.
            </p>
          </div>
        </div>
      `,
      text: `Reset Your Trodoo Password\n\nHello ${userName},\n\nWe received a request to reset your password for your Trodoo account.\n\nClick this link to reset your password:\n${resetUrl}\n\nThis link will expire in 1 hour for your security.\n\nIf you didn't request this password reset, you can safely ignore this email.\n\nNeed help? Contact <NAME_EMAIL>\n\nBest regards,\nThe Trodoo Team`,
    };
  }
}

// Export singleton instance
export const emailService = new EmailService();
