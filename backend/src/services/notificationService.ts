// Central notification service for Trodoo venue services
import { PocketBase, formatDate, delay } from "../../deps.ts";
import { createLogger, PerformanceLogger } from "../utils/logger.ts";
import { emailService } from "./emailService.ts";
import type { 
  User, 
  Booking, 
  Venue, 
  Notification, 
  NotificationType,
  BookingWithRelations,
  COLLECTIONS,
  SubscriptionData
} from "../types/pocketbase.ts";

const logger = createLogger("NotificationService");

export interface GuestInvitationData {
  bookingId: string;
  message: string;
  emails: string[];
}

export interface NotificationResult {
  success: boolean;
  sentCount?: number;
  error?: string;
}

class NotificationService {
  private pb: PocketBase | null = null;
  private isInitialized = false;
  private subscriptions: Array<() => void> = [];

  async initialize(): Promise<void> {
    const pbUrl = Deno.env.get("POCKETBASE_URL");
    const adminEmail = Deno.env.get("POCKETBASE_ADMIN_EMAIL");
    const adminPassword = Deno.env.get("POCKETBASE_ADMIN_PASSWORD");
    const nodeEnv = Deno.env.get("NODE_ENV") || "development";

    // Check if we have placeholder values or missing config
    const hasPlaceholderValues =
      !pbUrl || pbUrl.includes("localhost") ||
      !adminEmail || adminEmail.includes("admin@") ||
      !adminPassword || adminPassword.includes("your_");

    if (hasPlaceholderValues) {
      if (nodeEnv === "production") {
        throw new Error("PocketBase configuration missing in production");
      }

      // Development mode - use mock service
      logger.warn("Notification service running in mock mode (PocketBase not configured)");
      this.pb = null;
      this.isInitialized = true;
      return;
    }

    this.pb = new PocketBase(pbUrl);

    // Authenticate as admin
    await this.pb.admins.authWithPassword(adminEmail, adminPassword);

    // Setup real-time subscriptions
    await this.setupSubscriptions();

    this.isInitialized = true;
    logger.info("Notification service initialized");
  }

  isHealthy(): boolean {
    return this.isInitialized;
  }

  async cleanup(): Promise<void> {
    // Unsubscribe from all real-time subscriptions
    this.subscriptions.forEach(unsubscribe => unsubscribe());
    this.subscriptions = [];
    
    if (this.pb) {
      this.pb.authStore.clear();
    }
    
    logger.info("Notification service cleaned up");
  }

  private async setupSubscriptions(): Promise<void> {
    if (!this.pb) {
      logger.debug("Skipping notification subscriptions setup (mock mode)");
      return;
    }

    try {
      // Subscribe to user creation for welcome emails
      const userUnsubscribe = await this.pb.collection("users").subscribe("*", 
        (data: SubscriptionData<User>) => {
          if (data.action === "create") {
            this.handleNewUser(data.record).catch(error => {
              logger.error("Failed to handle new user", error, { userId: data.record.id });
            });
          }
        }
      );
      this.subscriptions.push(userUnsubscribe);

      // Subscribe to booking status changes
      const bookingUnsubscribe = await this.pb.collection("bookings").subscribe("*",
        (data: SubscriptionData<Booking>) => {
          if (data.action === "update") {
            this.handleBookingStatusChange(data.record).catch(error => {
              logger.error("Failed to handle booking status change", error, { bookingId: data.record.id });
            });
          }
        }
      );
      this.subscriptions.push(bookingUnsubscribe);

      logger.info("Real-time subscriptions established");
    } catch (error) {
      logger.error("Failed to setup subscriptions", error);
      throw error;
    }
  }

  private async handleNewUser(user: User): Promise<void> {
    logger.info("Handling new user registration", { userId: user.id, email: user.email });

    try {
      // Send welcome email
      const result = await emailService.sendWelcomeEmail(user.email, user.name || "");
      
      if (result.success) {
        // Record notification in database
        await this.recordNotification({
          user: user.id,
          type: "welcome",
          sent_at: new Date().toISOString(),
        });
        
        logger.info("Welcome email sent successfully", { userId: user.id });
      } else {
        logger.error("Failed to send welcome email", result.error, { userId: user.id });
      }
    } catch (error) {
      logger.error("Error handling new user", error, { userId: user.id });
    }
  }

  private async handleBookingStatusChange(booking: Booking): Promise<void> {
    logger.info("Handling booking status change", { 
      bookingId: booking.id, 
      status: booking.status 
    });

    try {
      // Get booking with expanded relations
      const fullBooking = await this.pb!.collection("bookings").getOne(booking.id, {
        expand: "renter,owner,venue"
      }) as BookingWithRelations;

      switch (booking.status) {
        case "confirmed":
          await this.sendBookingConfirmationEmail(fullBooking);
          break;
        case "denied":
          await this.sendBookingDenialEmail(fullBooking);
          break;
        case "paid":
          await this.sendPaymentSuccessEmail(fullBooking);
          break;
        default:
          // No notification needed for other statuses
          break;
      }
    } catch (error) {
      logger.error("Error handling booking status change", error, { bookingId: booking.id });
    }
  }

  private async sendBookingConfirmationEmail(booking: BookingWithRelations): Promise<void> {
    if (!booking.renter_expand || !booking.venue_expand) {
      logger.warn("Missing expanded data for booking confirmation", { bookingId: booking.id });
      return;
    }

    const result = await emailService.sendBookingConfirmationEmail(
      booking.renter_expand.email,
      booking.renter_expand.name || "",
      {
        venueName: booking.venue_expand.title,
        startDate: booking.start_date,
        endDate: booking.end_date,
        totalPrice: booking.total_price,
      }
    );

    if (result.success) {
      await this.recordNotification({
        booking: booking.id,
        user: booking.renter,
        type: "booking_confirmed",
        sent_at: new Date().toISOString(),
      });
      
      logger.info("Booking confirmation email sent", { bookingId: booking.id });
    } else {
      logger.error("Failed to send booking confirmation email", result.error, { bookingId: booking.id });
    }
  }

  private async sendBookingDenialEmail(booking: BookingWithRelations): Promise<void> {
    if (!booking.renter_expand || !booking.venue_expand) {
      logger.warn("Missing expanded data for booking denial", { bookingId: booking.id });
      return;
    }

    // For now, we'll use a simple denial notification
    // In a full implementation, we'd create a specific denial template
    logger.info("Booking denial notification", { 
      bookingId: booking.id,
      renterEmail: booking.renter_expand.email,
      venueName: booking.venue_expand.title
    });

    await this.recordNotification({
      booking: booking.id,
      user: booking.renter,
      type: "booking_denied",
      sent_at: new Date().toISOString(),
    });
  }

  private async sendPaymentSuccessEmail(booking: BookingWithRelations): Promise<void> {
    if (!booking.renter_expand || !booking.venue_expand) {
      logger.warn("Missing expanded data for payment success", { bookingId: booking.id });
      return;
    }

    const result = await emailService.sendPaymentSuccessEmail(
      booking.renter_expand.email,
      booking.renter_expand.name || "",
      {
        venueName: booking.venue_expand.title,
        startDate: booking.start_date,
        endDate: booking.end_date,
        totalPrice: booking.total_price,
        reference: booking.paystack_ref || "",
      }
    );

    if (result.success) {
      await this.recordNotification({
        booking: booking.id,
        user: booking.renter,
        type: "payment_success",
        sent_at: new Date().toISOString(),
      });
      
      logger.info("Payment success email sent", { bookingId: booking.id });
    } else {
      logger.error("Failed to send payment success email", result.error, { bookingId: booking.id });
    }
  }

  // Scheduled notification methods (called by cron jobs)
  async sendBookingReminders(): Promise<void> {
    const perfLogger = new PerformanceLogger("sendBookingReminders");
    
    try {
      // Find bookings starting in the next 24-25 hours
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      
      const dayAfter = new Date(tomorrow);
      dayAfter.setDate(dayAfter.getDate() + 1);

      const bookings = await this.pb!.collection("bookings").getFullList({
        filter: `status = "paid" && start_date >= "${tomorrow.toISOString()}" && start_date < "${dayAfter.toISOString()}"`,
        expand: "renter,venue"
      }) as BookingWithRelations[];

      let sentCount = 0;
      
      for (const booking of bookings) {
        // Check if reminder already sent
        const existingNotification = await this.pb!.collection("notifications").getFirstListItem(
          `booking = "${booking.id}" && type = "checkin_reminder"`,
          { requestKey: null }
        ).catch(() => null);

        if (existingNotification) {
          continue; // Already sent
        }

        if (booking.renter_expand && booking.venue_expand) {
          const result = await emailService.sendCheckinReminderEmail(
            booking.renter_expand.email,
            booking.renter_expand.name || "",
            {
              venueName: booking.venue_expand.title,
              venueAddress: typeof booking.venue_expand.address === 'string' 
                ? booking.venue_expand.address 
                : `${booking.venue_expand.address.street}, ${booking.venue_expand.address.city}`,
              startDate: booking.start_date,
            }
          );

          if (result.success) {
            await this.recordNotification({
              booking: booking.id,
              user: booking.renter,
              type: "checkin_reminder",
              sent_at: new Date().toISOString(),
            });
            sentCount++;
          }
        }
      }

      perfLogger.end({ sentCount });
      logger.info("Booking reminders sent", { sentCount });
    } catch (error) {
      perfLogger.endWithError(error);
      logger.error("Failed to send booking reminders", error);
    }
  }

  async sendCheckinReminders(): Promise<void> {
    const perfLogger = new PerformanceLogger("sendCheckinReminders");
    
    try {
      // Find bookings starting in the next 60-75 minutes
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
      const oneHour15Later = new Date(now.getTime() + 75 * 60 * 1000);

      const bookings = await this.pb!.collection("bookings").getFullList({
        filter: `status = "paid" && start_date >= "${oneHourLater.toISOString()}" && start_date <= "${oneHour15Later.toISOString()}"`,
        expand: "renter,venue"
      }) as BookingWithRelations[];

      let sentCount = 0;
      
      for (const booking of bookings) {
        // Check if check-in reminder already sent
        const existingNotification = await this.pb!.collection("notifications").getFirstListItem(
          `booking = "${booking.id}" && type = "checkin_reminder"`,
          { requestKey: null }
        ).catch(() => null);

        if (existingNotification) {
          continue; // Already sent
        }

        if (booking.renter_expand && booking.venue_expand) {
          const result = await emailService.sendCheckinReminderEmail(
            booking.renter_expand.email,
            booking.renter_expand.name || "",
            {
              venueName: booking.venue_expand.title,
              venueAddress: typeof booking.venue_expand.address === 'string' 
                ? booking.venue_expand.address 
                : `${booking.venue_expand.address.street}, ${booking.venue_expand.address.city}`,
              startDate: booking.start_date,
            }
          );

          if (result.success) {
            await this.recordNotification({
              booking: booking.id,
              user: booking.renter,
              type: "checkin_reminder",
              sent_at: new Date().toISOString(),
            });
            sentCount++;
          }
        }
      }

      perfLogger.end({ sentCount });
      logger.info("Check-in reminders sent", { sentCount });
    } catch (error) {
      perfLogger.endWithError(error);
      logger.error("Failed to send check-in reminders", error);
    }
  }

  async sendReviewPrompts(): Promise<void> {
    const perfLogger = new PerformanceLogger("sendReviewPrompts");
    
    try {
      // Find bookings that completed yesterday
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      yesterday.setHours(0, 0, 0, 0);
      
      const today = new Date(yesterday);
      today.setDate(today.getDate() + 1);

      const bookings = await this.pb!.collection("bookings").getFullList({
        filter: `status = "completed" && end_date >= "${yesterday.toISOString()}" && end_date < "${today.toISOString()}"`,
        expand: "renter,venue"
      }) as BookingWithRelations[];

      let sentCount = 0;
      
      for (const booking of bookings) {
        // Check if review prompt already sent
        const existingNotification = await this.pb!.collection("notifications").getFirstListItem(
          `booking = "${booking.id}" && type = "review_prompt"`,
          { requestKey: null }
        ).catch(() => null);

        if (existingNotification) {
          continue; // Already sent
        }

        // Check if review already exists
        const existingReview = await this.pb!.collection("reviews").getFirstListItem(
          `booking = "${booking.id}"`,
          { requestKey: null }
        ).catch(() => null);

        if (existingReview) {
          continue; // Review already submitted
        }

        if (booking.renter_expand && booking.venue_expand) {
          const frontendUrl = Deno.env.get("FRONTEND_URL") || "http://localhost:4321";
          const reviewUrl = `${frontendUrl}/bookings/${booking.id}#review`;

          const result = await emailService.sendReviewPromptEmail(
            booking.renter_expand.email,
            booking.renter_expand.name || "",
            {
              venueName: booking.venue_expand.title,
              reviewUrl,
            }
          );

          if (result.success) {
            await this.recordNotification({
              booking: booking.id,
              user: booking.renter,
              type: "review_prompt",
              sent_at: new Date().toISOString(),
            });
            sentCount++;
          }
        }
      }

      perfLogger.end({ sentCount });
      logger.info("Review prompts sent", { sentCount });
    } catch (error) {
      perfLogger.endWithError(error);
      logger.error("Failed to send review prompts", error);
    }
  }

  // Guest invitation method (called from API endpoint)
  async sendGuestInvitations(data: GuestInvitationData): Promise<NotificationResult> {
    const perfLogger = new PerformanceLogger("sendGuestInvitations");
    
    try {
      // Get booking details
      const booking = await this.pb!.collection("bookings").getOne(data.bookingId, {
        expand: "renter,venue"
      }) as BookingWithRelations;

      if (!booking.renter_expand || !booking.venue_expand) {
        return { success: false, error: "Missing booking data" };
      }

      let sentCount = 0;
      const errors: string[] = [];

      for (const email of data.emails) {
        try {
          const result = await emailService.sendGuestInvitationEmail(
            email,
            booking.renter_expand.name || booking.renter_expand.email,
            {
              venueName: booking.venue_expand.title,
              venueAddress: typeof booking.venue_expand.address === 'string' 
                ? booking.venue_expand.address 
                : `${booking.venue_expand.address.street}, ${booking.venue_expand.address.city}`,
              startDate: booking.start_date,
              endDate: booking.end_date,
              message: data.message,
            }
          );

          if (result.success) {
            sentCount++;
          } else {
            errors.push(`Failed to send to ${email}: ${result.error}`);
          }
        } catch (error) {
          errors.push(`Error sending to ${email}: ${error.message}`);
        }
      }

      // Record notification
      if (sentCount > 0) {
        await this.recordNotification({
          booking: booking.id,
          user: booking.renter,
          type: "invitation_sent",
          sent_at: new Date().toISOString(),
        });
      }

      perfLogger.end({ sentCount, totalEmails: data.emails.length });
      
      if (errors.length > 0) {
        logger.warn("Some invitations failed", { errors, sentCount });
      }

      return {
        success: sentCount > 0,
        sentCount,
        error: errors.length > 0 ? errors.join("; ") : undefined,
      };
    } catch (error) {
      perfLogger.endWithError(error);
      logger.error("Failed to send guest invitations", error);
      return { success: false, error: error.message };
    }
  }

  private async recordNotification(notification: Omit<Notification, "id" | "created" | "updated">): Promise<void> {
    try {
      await this.pb!.collection("notifications").create(notification);
    } catch (error) {
      logger.error("Failed to record notification", error, { notification });
    }
  }
}

// Export singleton instance
export const notificationService = new NotificationService();
