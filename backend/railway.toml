[build]
builder = "dockerfile"
dockerfilePath = "Dockerfile"

[deploy]
healthcheckPath = "/health"
healthcheckTimeout = 300
restartPolicyType = "on_failure"
restartPolicyMaxRetries = 3

[env]
PORT = "8000"
NODE_ENV = "production"

# These will be set in Railway dashboard
# POCKETBASE_URL = "${{POCKETBASE_URL}}"
# POCKETBASE_ADMIN_EMAIL = "${{POCKETBASE_ADMIN_EMAIL}}"
# POCKETBASE_ADMIN_PASSWORD = "${{POCKETBASE_ADMIN_PASSWORD}}"
# RESEND_API_KEY = "${{RESEND_API_KEY}}"
# PAYSTACK_SECRET_KEY = "${{PAYSTACK_SECRET_KEY}}"
# PAYSTACK_WEBHOOK_SECRET = "${{PAYSTACK_WEBHOOK_SECRET}}"
# FRONTEND_URL = "${{FRONTEND_URL}}"

# Optional services - will use mock mode if not configured
# MEILISEARCH_HOST = "${{MEILISEARCH_HOST}}"
# MEILISEARCH_API_KEY = "${{MEILISEARCH_API_KEY}}"
