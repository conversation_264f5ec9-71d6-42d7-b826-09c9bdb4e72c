// Basic test script for Deno Venue Services
import { load } from "./deps.ts";

// Load environment variables
await load({ allowEmptyValues: true });

console.log("🧪 Testing Deno Venue Services");
console.log("================================");

// Test 1: Environment Variables
console.log("\n1. Testing Environment Variables:");
const requiredEnvVars = [
  "POCKETBASE_URL",
  "POCKETBASE_ADMIN_EMAIL", 
  "POCKETBASE_ADMIN_PASSWORD",
  "RESEND_API_KEY",
  "PAYSTACK_SECRET_KEY",
  "PAYSTACK_WEBHOOK_SECRET"
];

let envTestsPassed = 0;
for (const envVar of requiredEnvVars) {
  const value = Deno.env.get(envVar);
  if (value) {
    console.log(`   ✅ ${envVar}: Set`);
    envTestsPassed++;
  } else {
    console.log(`   ❌ ${envVar}: Missing`);
  }
}

console.log(`   📊 Environment Variables: ${envTestsPassed}/${requiredEnvVars.length} passed`);

// Test 2: Service Health Checks
console.log("\n2. Testing Service Health:");

try {
  // Test PocketBase connection
  const pbUrl = Deno.env.get("POCKETBASE_URL");
  if (pbUrl) {
    const pbResponse = await fetch(`${pbUrl}/api/health`);
    if (pbResponse.ok) {
      console.log("   ✅ PocketBase: Connected");
    } else {
      console.log(`   ❌ PocketBase: HTTP ${pbResponse.status}`);
    }
  } else {
    console.log("   ❌ PocketBase: URL not configured");
  }
} catch (error) {
  console.log(`   ❌ PocketBase: ${error.message}`);
}

try {
  // Test MeiliSearch connection (if configured)
  const meiliHost = Deno.env.get("MEILISEARCH_HOST");
  if (meiliHost) {
    const meiliResponse = await fetch(`${meiliHost}/health`);
    if (meiliResponse.ok) {
      console.log("   ✅ MeiliSearch: Connected");
    } else {
      console.log(`   ❌ MeiliSearch: HTTP ${meiliResponse.status}`);
    }
  } else {
    console.log("   ⚠️  MeiliSearch: Not configured (optional)");
  }
} catch (error) {
  console.log(`   ❌ MeiliSearch: ${error.message}`);
}

try {
  // Test Paystack API
  const paystackKey = Deno.env.get("PAYSTACK_SECRET_KEY");
  if (paystackKey) {
    const paystackResponse = await fetch("https://api.paystack.co/bank", {
      headers: {
        "Authorization": `Bearer ${paystackKey}`,
        "Content-Type": "application/json"
      }
    });
    if (paystackResponse.ok) {
      console.log("   ✅ Paystack API: Connected");
    } else {
      console.log(`   ❌ Paystack API: HTTP ${paystackResponse.status}`);
    }
  } else {
    console.log("   ❌ Paystack API: Key not configured");
  }
} catch (error) {
  console.log(`   ❌ Paystack API: ${error.message}`);
}

// Test 3: Service Imports
console.log("\n3. Testing Service Imports:");

try {
  const { emailService } = await import("./src/services/emailService.ts");
  console.log("   ✅ Email Service: Imported");
  
  const { notificationService } = await import("./src/services/notificationService.ts");
  console.log("   ✅ Notification Service: Imported");
  
  const { meilisearchSyncService } = await import("./src/services/meilisearchSyncService.ts");
  console.log("   ✅ MeiliSearch Sync Service: Imported");
  
  const { paystackWebhookService } = await import("./src/services/paystackWebhookService.ts");
  console.log("   ✅ Paystack Webhook Service: Imported");
  
  const { payoutService } = await import("./src/services/payoutService.ts");
  console.log("   ✅ Payout Service: Imported");
} catch (error) {
  console.log(`   ❌ Service Import Failed: ${error.message}`);
}

// Test 4: Logger
console.log("\n4. Testing Logger:");

try {
  const { createLogger } = await import("./src/utils/logger.ts");
  const testLogger = createLogger("Test");
  testLogger.info("Logger test successful");
  console.log("   ✅ Logger: Working");
} catch (error) {
  console.log(`   ❌ Logger: ${error.message}`);
}

// Test 5: Type Definitions
console.log("\n5. Testing Type Definitions:");

try {
  await import("./src/types/pocketbase.ts");
  console.log("   ✅ PocketBase Types: Imported");
  
  await import("./src/types/paystack.ts");
  console.log("   ✅ Paystack Types: Imported");
} catch (error) {
  console.log(`   ❌ Type Import Failed: ${error.message}`);
}

console.log("\n================================");
console.log("🎯 Test Summary:");
console.log(`   Environment: ${envTestsPassed}/${requiredEnvVars.length} variables configured`);
console.log("   Services: Check individual results above");
console.log("   Ready for deployment: " + (envTestsPassed >= 4 ? "✅ Yes" : "❌ No"));

console.log("\n💡 Next Steps:");
console.log("   1. Configure missing environment variables");
console.log("   2. Start the service: deno run --allow-net --allow-env --allow-read main.ts");
console.log("   3. Test endpoints: curl http://localhost:8000/health");
console.log("   4. Monitor logs for any issues");

console.log("\n🚀 Service will be available at: http://localhost:8000");
console.log("   Health check: GET /health");
console.log("   Paystack webhook: POST /webhooks/paystack");
console.log("   Guest invitations: POST /api/internal/send-invitations");
